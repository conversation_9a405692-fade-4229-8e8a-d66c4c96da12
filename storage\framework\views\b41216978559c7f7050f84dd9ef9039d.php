<?php $__env->startSection('content'); ?>
<main class="app-main">
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <!--begin::Container-->
        <div class="container-fluid">
            <!--begin::Row-->
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">Manage Permissions: <?php echo e($role->name); ?></h3>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="<?php echo e(route('role-management.index')); ?>">Roles & Permissions</a></li>
                        <li class="breadcrumb-item"><a href="<?php echo e(route('role-management.show', $role)); ?>"><?php echo e($role->name); ?></a></li>
                        <li class="breadcrumb-item active" aria-current="page">Permissions</li>
                    </ol>
                </div>
            </div>
            <!--end::Row-->
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content Header-->

    <!--begin::App Content-->
    <div class="app-content">
        <!--begin::Container-->
        <div class="container-fluid">
            <!--begin::Row-->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="card-title">
                                        <span class="badge <?php echo e($role->badge_class); ?> me-2"><?php echo e(strtoupper(substr($role->name, 0, 2))); ?></span>
                                        <?php echo e($role->name); ?> Permissions
                                    </h3>
                                    <p class="text-muted mb-0">Select permissions to assign to this role</p>
                                </div>
                                <div>
                                    <button type="button" class="btn btn-outline-success me-2" id="selectAllBtn">
                                        <i class="bi bi-check-all me-1"></i>Select All
                                    </button>
                                    <button type="button" class="btn btn-outline-warning" id="clearAllBtn">
                                        <i class="bi bi-x-circle me-1"></i>Clear All
                                    </button>
                                </div>
                            </div>
                        </div>

                        <form action="<?php echo e(route('role-management.update-permissions', $role)); ?>" method="POST" id="permissionsForm">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('PUT'); ?>
                            
                            <div class="card-body">
                                <!-- Permission Statistics -->
                                <div class="row mb-4">
                                    <div class="col-md-3">
                                        <div class="card bg-primary text-white">
                                            <div class="card-body text-center">
                                                <h4 id="selectedCount"><?php echo e(count($rolePermissions)); ?></h4>
                                                <small>Selected Permissions</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card bg-info text-white">
                                            <div class="card-body text-center">
                                                <h4><?php echo e($permissions->flatten()->count()); ?></h4>
                                                <small>Total Permissions</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card bg-success text-white">
                                            <div class="card-body text-center">
                                                <h4><?php echo e($permissions->count()); ?></h4>
                                                <small>Permission Groups</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card bg-warning text-dark">
                                            <div class="card-body text-center">
                                                <h4><?php echo e($role->level); ?></h4>
                                                <small>Role Level</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Search and Filter -->
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="bi bi-search"></i></span>
                                            <input type="text" class="form-control" id="searchPermissions" placeholder="Search permissions...">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <select class="form-select" id="filterGroup">
                                            <option value="">All Groups</option>
                                            <?php $__currentLoopData = $permissions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $group => $groupPermissions): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($group); ?>"><?php echo e(ucwords(str_replace('_', ' ', $group))); ?></option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                    </div>
                                </div>

                                <!-- Permissions by Group -->
                                <?php $__currentLoopData = $permissions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $group => $groupPermissions): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="permission-group mb-4" data-group="<?php echo e($group); ?>">
                                        <div class="card">
                                            <div class="card-header bg-light">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <h5 class="mb-0">
                                                        <i class="bi bi-folder me-2"></i>
                                                        <?php echo e(ucwords(str_replace('_', ' ', $group))); ?>

                                                        <span class="badge bg-secondary ms-2"><?php echo e($groupPermissions->count()); ?></span>
                                                    </h5>
                                                    <div>
                                                        <button type="button" class="btn btn-sm btn-outline-primary group-select-all" data-group="<?php echo e($group); ?>">
                                                            <i class="bi bi-check-all me-1"></i>Select All
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-outline-secondary group-clear-all" data-group="<?php echo e($group); ?>">
                                                            <i class="bi bi-x-circle me-1"></i>Clear All
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="card-body">
                                                <div class="row">
                                                    <?php $__currentLoopData = $groupPermissions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $permission): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <div class="col-md-6 col-lg-4 mb-2 permission-item" data-permission-name="<?php echo e(strtolower($permission->name)); ?>">
                                                            <div class="form-check">
                                                                <input class="form-check-input permission-checkbox" 
                                                                       type="checkbox" 
                                                                       name="permissions[]" 
                                                                       value="<?php echo e($permission->id); ?>" 
                                                                       id="permission_<?php echo e($permission->id); ?>"
                                                                       <?php echo e(in_array($permission->id, $rolePermissions) ? 'checked' : ''); ?>

                                                                       data-group="<?php echo e($group); ?>">
                                                                <label class="form-check-label" for="permission_<?php echo e($permission->id); ?>">
                                                                    <strong><?php echo e($permission->name); ?></strong>
                                                                    <?php if($permission->description): ?>
                                                                        <br><small class="text-muted"><?php echo e($permission->description); ?></small>
                                                                    <?php endif; ?>
                                                                    <br><code class="text-primary"><?php echo e($permission->slug); ?></code>
                                                                </label>
                                                            </div>
                                                        </div>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>

                            <div class="card-footer">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <a href="<?php echo e(route('role-management.show', $role)); ?>" class="btn btn-secondary">
                                            <i class="bi bi-arrow-left me-2"></i>Back to Role
                                        </a>
                                    </div>
                                    <div>
                                        <span class="text-muted me-3">
                                            <span id="selectedCountText"><?php echo e(count($rolePermissions)); ?></span> permissions selected
                                        </span>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="bi bi-check-circle me-2"></i>Update Permissions
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <!--end::Row-->
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content-->
</main>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const checkboxes = document.querySelectorAll('.permission-checkbox');
    const selectedCountElement = document.getElementById('selectedCount');
    const selectedCountTextElement = document.getElementById('selectedCountText');
    const searchInput = document.getElementById('searchPermissions');
    const filterGroup = document.getElementById('filterGroup');
    
    // Update selected count
    function updateSelectedCount() {
        const selectedCount = document.querySelectorAll('.permission-checkbox:checked').length;
        selectedCountElement.textContent = selectedCount;
        selectedCountTextElement.textContent = selectedCount;
    }
    
    // Search functionality
    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const permissionItems = document.querySelectorAll('.permission-item');
        
        permissionItems.forEach(item => {
            const permissionName = item.dataset.permissionName;
            if (permissionName.includes(searchTerm)) {
                item.style.display = '';
            } else {
                item.style.display = 'none';
            }
        });
    });
    
    // Group filter
    filterGroup.addEventListener('change', function() {
        const selectedGroup = this.value;
        const permissionGroups = document.querySelectorAll('.permission-group');
        
        permissionGroups.forEach(group => {
            if (selectedGroup === '' || group.dataset.group === selectedGroup) {
                group.style.display = '';
            } else {
                group.style.display = 'none';
            }
        });
    });
    
    // Select all permissions
    document.getElementById('selectAllBtn').addEventListener('click', function() {
        checkboxes.forEach(checkbox => {
            if (checkbox.closest('.permission-item').style.display !== 'none') {
                checkbox.checked = true;
            }
        });
        updateSelectedCount();
    });
    
    // Clear all permissions
    document.getElementById('clearAllBtn').addEventListener('click', function() {
        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
        });
        updateSelectedCount();
    });
    
    // Group select all
    document.querySelectorAll('.group-select-all').forEach(button => {
        button.addEventListener('click', function() {
            const group = this.dataset.group;
            const groupCheckboxes = document.querySelectorAll(`[data-group="${group}"]`);
            groupCheckboxes.forEach(checkbox => {
                checkbox.checked = true;
            });
            updateSelectedCount();
        });
    });
    
    // Group clear all
    document.querySelectorAll('.group-clear-all').forEach(button => {
        button.addEventListener('click', function() {
            const group = this.dataset.group;
            const groupCheckboxes = document.querySelectorAll(`[data-group="${group}"]`);
            groupCheckboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
            updateSelectedCount();
        });
    });
    
    // Update count when individual checkboxes change
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectedCount);
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.main', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\Marbar-Africa\Marbar-AFRICA\resources\views/admin/role-management/permissions.blade.php ENDPATH**/ ?>