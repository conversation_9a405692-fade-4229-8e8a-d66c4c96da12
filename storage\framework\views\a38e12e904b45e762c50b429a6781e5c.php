<?php $__env->startSection('title', 'Diagnoses Management'); ?>

<?php $__env->startSection('content'); ?>
<main class="app-main">
    <div class="app-content-header">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">Diagnoses Management</h3>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">Home</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Diagnoses Management</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="app-content">
        <div class="container-fluid">
            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-lg-3 col-6">
                    <div class="small-box text-bg-primary">
                        <div class="inner">
                            <h3><?php echo e($stats['total_diagnoses']); ?></h3>
                            <p>Total Diagnoses</p>
                        </div>
                        <div class="small-box-icon">
                            <i class="bi bi-clipboard2-pulse"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box text-bg-success">
                        <div class="inner">
                            <h3><?php echo e($stats['active_diagnoses']); ?></h3>
                            <p>Active Diagnoses</p>
                        </div>
                        <div class="small-box-icon">
                            <i class="bi bi-check-circle"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box text-bg-warning">
                        <div class="inner">
                            <h3><?php echo e($stats['categories_count']); ?></h3>
                            <p>Categories</p>
                        </div>
                        <div class="small-box-icon">
                            <i class="bi bi-tags"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box text-bg-info">
                        <div class="inner">
                            <h3><?php echo e($stats['recent_additions']); ?></h3>
                            <p>Recent Additions</p>
                        </div>
                        <div class="small-box-icon">
                            <i class="bi bi-plus-circle"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content Card -->
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h3 class="card-title">
                                <i class="bi bi-clipboard2-pulse me-2"></i>
                                All Diagnoses
                            </h3>
                        </div>
                        <div class="col-auto">
                            <div class="btn-group" role="group">
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('diagnoses.create')): ?>
                                <a href="<?php echo e(route('diagnoses-management.create')); ?>" class="btn btn-primary">
                                    <i class="bi bi-plus-circle me-1"></i>
                                    Add New Diagnosis
                                </a>
                                <?php endif; ?>

                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('diagnoses.create')): ?>
                                <a href="<?php echo e(route('diagnoses-management.upload')); ?>" class="btn btn-success">
                                    <i class="bi bi-cloud-upload me-1"></i>
                                    Bulk Upload
                                </a>
                                <?php endif; ?>

                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('diagnoses.view')): ?>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                        <i class="bi bi-download me-1"></i>
                                        Export
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li>
                                            <a class="dropdown-item" href="<?php echo e(route('diagnoses-management.export', ['format' => 'csv'])); ?>">
                                                <i class="bi bi-filetype-csv me-1"></i>
                                                Export as CSV
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="<?php echo e(route('diagnoses-management.export', ['format' => 'excel'])); ?>">
                                                <i class="bi bi-file-earmark-excel me-1"></i>
                                                Export as Excel
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Search and Filter -->
                    <form method="GET" action="<?php echo e(route('diagnoses-management.index')); ?>">
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-search"></i>
                                    </span>
                                    <input type="text" class="form-control" name="search" placeholder="Search diagnoses..." value="<?php echo e(request('search')); ?>">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" name="category">
                                    <option value="">All Categories</option>
                                    <option value="Mood Disorders" <?php echo e(request('category') == 'Mood Disorders' ? 'selected' : ''); ?>>Mood Disorders</option>
                                    <option value="Anxiety Disorders" <?php echo e(request('category') == 'Anxiety Disorders' ? 'selected' : ''); ?>>Anxiety Disorders</option>
                                    <option value="Neurodevelopmental Disorders" <?php echo e(request('category') == 'Neurodevelopmental Disorders' ? 'selected' : ''); ?>>Neurodevelopmental Disorders</option>
                                    <option value="Trauma and Stressor-Related Disorders" <?php echo e(request('category') == 'Trauma and Stressor-Related Disorders' ? 'selected' : ''); ?>>Trauma and Stressor-Related Disorders</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" name="severity">
                                    <option value="">All Severity Levels</option>
                                    <option value="mild" <?php echo e(request('severity') == 'mild' ? 'selected' : ''); ?>>Mild</option>
                                    <option value="moderate" <?php echo e(request('severity') == 'moderate' ? 'selected' : ''); ?>>Moderate</option>
                                    <option value="severe" <?php echo e(request('severity') == 'severe' ? 'selected' : ''); ?>>Severe</option>
                                    <option value="critical" <?php echo e(request('severity') == 'critical' ? 'selected' : ''); ?>>Critical</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" name="status">
                                    <option value="">All Status</option>
                                    <option value="active" <?php echo e(request('status') == 'active' ? 'selected' : ''); ?>>Active</option>
                                    <option value="inactive" <?php echo e(request('status') == 'inactive' ? 'selected' : ''); ?>>Inactive</option>
                                </select>
                            </div>
                            <div class="col-md-1">
                                <select class="form-select" name="per_page">
                                    <option value="15" <?php echo e(request('per_page', 15) == 15 ? 'selected' : ''); ?>>15</option>
                                    <option value="25" <?php echo e(request('per_page') == 25 ? 'selected' : ''); ?>>25</option>
                                    <option value="50" <?php echo e(request('per_page') == 50 ? 'selected' : ''); ?>>50</option>
                                    <option value="100" <?php echo e(request('per_page') == 100 ? 'selected' : ''); ?>>100</option>
                                </select>
                            </div>
                            <div class="col-md-1">
                                <div class="d-flex gap-1">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-search"></i>
                                    </button>
                                    <a href="<?php echo e(route('diagnoses-management.index')); ?>" class="btn btn-outline-secondary">
                                        <i class="bi bi-arrow-clockwise"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- Diagnoses Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="diagnosesTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>Code</th>
                                    <th>Name</th>
                                    <th>Category</th>
                                    <th>Severity</th>
                                    <th>Usage Count</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $diagnoses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $diagnosis): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php
                                    $code = is_array($diagnosis) ? $diagnosis['code'] : $diagnosis->code;
                                    $name = is_array($diagnosis) ? $diagnosis['name'] : $diagnosis->name;
                                    $category = is_array($diagnosis) ? $diagnosis['category'] : ($diagnosis->category ? $diagnosis->category->name : 'Uncategorized');
                                    $severity_level = is_array($diagnosis) ? $diagnosis['severity_level'] : $diagnosis->severity_level;
                                    $usage_count = is_array($diagnosis) ? $diagnosis['usage_count'] : $diagnosis->usage_count;
                                    $is_active = is_array($diagnosis) ? $diagnosis['is_active'] : $diagnosis->is_active;
                                    $diagnosis_id = is_array($diagnosis) ? $diagnosis['id'] : $diagnosis->id;
                                ?>
                                <tr>
                                    <td>
                                        <code class="text-primary"><?php echo e($code); ?></code>
                                    </td>
                                    <td>
                                        <strong><?php echo e($name); ?></strong>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary"><?php echo e($category); ?></span>
                                    </td>
                                    <td>
                                        <span class="badge
                                            <?php if($severity_level === 'mild'): ?> bg-success
                                            <?php elseif($severity_level === 'moderate'): ?> bg-warning
                                            <?php elseif($severity_level === 'severe'): ?> bg-danger
                                            <?php else: ?> bg-dark
                                            <?php endif; ?>">
                                            <?php echo e(ucfirst($severity_level)); ?>

                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-info"><?php echo e($usage_count); ?></span>
                                    </td>
                                    <td>
                                        <?php if($is_active): ?>
                                            <span class="badge bg-success">Active</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">Inactive</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('diagnoses.view')): ?>
                                            <a href="<?php echo e(route('diagnoses-management.show', $diagnosis_id)); ?>"
                                               class="btn btn-outline-info" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            <?php endif; ?>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('diagnoses.edit')): ?>
                                            <a href="<?php echo e(route('diagnoses-management.edit', $diagnosis_id)); ?>"
                                               class="btn btn-outline-warning" title="Edit">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            <?php endif; ?>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('diagnoses.delete')): ?>
                                            <button type="button" class="btn btn-outline-danger"
                                                    onclick="confirmDelete(<?php echo e($diagnosis_id); ?>)" title="Delete">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <?php if($diagnoses->hasPages()): ?>
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <div>
                            <p class="text-muted mb-0">
                                Showing <?php echo e($diagnoses->firstItem()); ?> to <?php echo e($diagnoses->lastItem()); ?> of <?php echo e($diagnoses->total()); ?> diagnoses
                            </p>
                        </div>
                        <div>
                            <?php echo e($diagnoses->appends(request()->query())->links('custom.pagination')); ?>

                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</main>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    Confirm Delete
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="bi bi-warning me-2"></i>
                    <strong>Warning:</strong> This action cannot be undone!
                </div>
                <p>Are you sure you want to delete the following diagnosis?</p>
                <div class="card bg-light">
                    <div class="card-body">
                        <h6 class="card-title mb-1" id="deleteItemCode">-</h6>
                        <p class="card-text mb-0" id="deleteItemName">-</p>
                    </div>
                </div>
                <div class="mt-3">
                    <small class="text-muted">
                        <i class="bi bi-info-circle me-1"></i>
                        This will permanently remove the diagnosis from the system.
                    </small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-lg me-1"></i>
                    Cancel
                </button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('DELETE'); ?>
                    <button type="submit" class="btn btn-danger" id="confirmDeleteBtn">
                        <i class="bi bi-trash me-1"></i>
                        <span class="btn-text">Delete Diagnosis</span>
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
// Auto-submit form when per_page changes
document.addEventListener('DOMContentLoaded', function() {
    const perPageSelect = document.querySelector('select[name="per_page"]');
    if (perPageSelect) {
        perPageSelect.addEventListener('change', function() {
            this.form.submit();
        });
    }
});

function confirmDelete(id) {
    // Find the diagnosis row to get details
    const table = document.getElementById('diagnosesTable');
    const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');
    let diagnosisCode = '';
    let diagnosisName = '';

    for (let i = 0; i < rows.length; i++) {
        const row = rows[i];
        const deleteBtn = row.querySelector(`button[onclick="confirmDelete(${id})"]`);
        if (deleteBtn) {
            diagnosisCode = row.cells[0].textContent.trim();
            diagnosisName = row.cells[1].textContent.trim();
            break;
        }
    }

    // Update modal content
    document.getElementById('deleteItemCode').textContent = diagnosisCode;
    document.getElementById('deleteItemName').textContent = diagnosisName;

    // Update form action
    const form = document.getElementById('deleteForm');
    form.action = `/diagnoses-management/destroy/${id}`;

    // Add loading state to delete button
    const deleteBtn = document.getElementById('confirmDeleteBtn');
    const originalText = deleteBtn.innerHTML;

    form.addEventListener('submit', function(e) {
        deleteBtn.disabled = true;
        deleteBtn.innerHTML = '<i class="bi bi-spinner-border spinner-border-sm me-1"></i>Deleting...';

        // Re-enable button after a delay in case of errors
        setTimeout(() => {
            deleteBtn.disabled = false;
            deleteBtn.innerHTML = originalText;
        }, 5000);
    }, { once: true });

    // Show modal
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\Marbar-Africa\Marbar-AFRICA\resources\views/admin/management/diagnoses/index.blade.php ENDPATH**/ ?>