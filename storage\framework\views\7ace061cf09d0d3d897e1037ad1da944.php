<?php $__env->startSection('title', 'Diagnosis Details'); ?>

<?php $__env->startSection('content'); ?>
<main class="app-main">
    <div class="app-content-header">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">Diagnosis Details</h3>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?php echo e(route('diagnoses-management.index')); ?>">Diagnoses Management</a></li>
                        <li class="breadcrumb-item active" aria-current="page"><?php echo e($diagnosis['code']); ?></li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="app-content">
        <div class="container-fluid">
            <div class="row">
                <!-- Main Details -->
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h3 class="card-title">
                                    <i class="bi bi-clipboard2-pulse me-2"></i>
                                    <?php echo e($diagnosis['name']); ?>

                                </h3>
                                <div>
                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('diagnoses.edit')): ?>
                                    <a href="<?php echo e(route('diagnoses-management.edit', $diagnosis['id'])); ?>" 
                                       class="btn btn-warning btn-sm">
                                        <i class="bi bi-pencil me-1"></i>
                                        Edit
                                    </a>
                                    <?php endif; ?>
                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('diagnoses.delete')): ?>
                                    <button type="button" 
                                            class="btn btn-danger btn-sm" 
                                            onclick="confirmDelete(<?php echo e($diagnosis['id']); ?>)">
                                        <i class="bi bi-trash me-1"></i>
                                        Delete
                                    </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- Basic Information -->
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <h5 class="text-muted mb-3">Basic Information</h5>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td class="fw-bold" style="width: 40%;">Diagnosis Code:</td>
                                            <td>
                                                <code class="fs-5 text-primary"><?php echo e($diagnosis['code']); ?></code>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Category:</td>
                                            <td>
                                                <span class="badge bg-secondary fs-6"><?php echo e($diagnosis['category']); ?></span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Severity Level:</td>
                                            <td>
                                                <span class="badge fs-6
                                                    <?php if($diagnosis['severity_level'] === 'mild'): ?> bg-success
                                                    <?php elseif($diagnosis['severity_level'] === 'moderate'): ?> bg-warning
                                                    <?php elseif($diagnosis['severity_level'] === 'severe'): ?> bg-danger
                                                    <?php else: ?> bg-dark
                                                    <?php endif; ?>">
                                                    <?php echo e(ucfirst($diagnosis['severity_level'])); ?>

                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Status:</td>
                                            <td>
                                                <?php if($diagnosis['is_active']): ?>
                                                    <span class="badge bg-success fs-6">Active</span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary fs-6">Inactive</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <h5 class="text-muted mb-3">Usage Statistics</h5>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td class="fw-bold" style="width: 40%;">Usage Count:</td>
                                            <td>
                                                <span class="badge bg-info fs-6"><?php echo e($diagnosis['usage_count']); ?></span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Created:</td>
                                            <td><?php echo e($diagnosis['created_at']->format('M d, Y')); ?></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Days Active:</td>
                                            <td><?php echo e($diagnosis['created_at']->diffInDays(now())); ?> days</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>

                            <!-- Description -->
                            <?php if(isset($diagnosis['description']) && $diagnosis['description']): ?>
                            <div class="mb-4">
                                <h5 class="text-muted mb-3">Description</h5>
                                <div class="alert alert-light">
                                    <p class="mb-0"><?php echo e($diagnosis['description'] ?? 'No description available.'); ?></p>
                                </div>
                            </div>
                            <?php endif; ?>

                            <!-- Additional Information -->
                            <div class="row">
                                <div class="col-12">
                                    <h5 class="text-muted mb-3">Clinical Information</h5>
                                    <div class="alert alert-info">
                                        <h6 class="alert-heading">
                                            <i class="bi bi-info-circle me-1"></i>
                                            Clinical Notes
                                        </h6>
                                        <p class="mb-2">
                                            <strong>Diagnostic Criteria:</strong> This diagnosis follows standard clinical guidelines and diagnostic criteria.
                                        </p>
                                        <p class="mb-2">
                                            <strong>Treatment Considerations:</strong> Treatment approaches should be individualized based on patient presentation and severity.
                                        </p>
                                        <p class="mb-0">
                                            <strong>Follow-up:</strong> Regular monitoring and assessment recommended for optimal patient outcomes.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="col-lg-4">
                    <!-- Quick Actions -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-lightning me-1"></i>
                                Quick Actions
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('diagnoses.edit')): ?>
                                <a href="<?php echo e(route('diagnoses-management.edit', $diagnosis['id'])); ?>" 
                                   class="btn btn-outline-warning">
                                    <i class="bi bi-pencil me-1"></i>
                                    Edit Diagnosis
                                </a>
                                <?php endif; ?>
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('diagnoses.create')): ?>
                                <a href="<?php echo e(route('diagnoses-management.create')); ?>" 
                                   class="btn btn-outline-primary">
                                    <i class="bi bi-plus-circle me-1"></i>
                                    Add New Diagnosis
                                </a>
                                <?php endif; ?>
                                <a href="<?php echo e(route('diagnoses-management.index')); ?>" 
                                   class="btn btn-outline-secondary">
                                    <i class="bi bi-list me-1"></i>
                                    View All Diagnoses
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Related Information -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-link-45deg me-1"></i>
                                Related Information
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="list-group list-group-flush">
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('diagnoses.categories')): ?>
                                <a href="<?php echo e(route('diagnoses-management.categories.index')); ?>" 
                                   class="list-group-item list-group-item-action">
                                    <i class="bi bi-tags me-2"></i>
                                    Diagnosis Categories
                                </a>
                                <?php endif; ?>
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('diagnoses.analytics')): ?>
                                <a href="<?php echo e(route('diagnoses-management.analytics.index')); ?>" 
                                   class="list-group-item list-group-item-action">
                                    <i class="bi bi-graph-up me-2"></i>
                                    Diagnosis Analytics
                                </a>
                                <?php endif; ?>
                                <a href="#" class="list-group-item list-group-item-action">
                                    <i class="bi bi-file-medical me-2"></i>
                                    Treatment Guidelines
                                </a>
                                <a href="#" class="list-group-item list-group-item-action">
                                    <i class="bi bi-people me-2"></i>
                                    Patient Cases
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this diagnosis?</p>
                <div class="alert alert-warning">
                    <strong>Warning:</strong> This action cannot be undone. The diagnosis "<?php echo e($diagnosis['code']); ?> - <?php echo e($diagnosis['name']); ?>" will be permanently removed.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('DELETE'); ?>
                    <button type="submit" class="btn btn-danger">Delete Diagnosis</button>
                </form>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
function confirmDelete(id) {
    const form = document.getElementById('deleteForm');
    form.action = `/diagnoses-management/destroy/${id}`;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\Marbar-Africa\Marbar-AFRICA\resources\views/admin/management/diagnoses/show.blade.php ENDPATH**/ ?>