<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Role;
use App\Models\Permission;

class AssignRolePermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'role:assign-permissions 
                            {role : The role name or ID}
                            {permissions* : Permission names to assign}
                            {--sync : Sync permissions (remove existing ones not in the list)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Assign permissions to a role';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $roleIdentifier = $this->argument('role');
        $permissionNames = $this->argument('permissions');
        $sync = $this->option('sync');

        // Find the role
        $role = Role::where('name', $roleIdentifier)
                   ->orWhere('id', $roleIdentifier)
                   ->first();

        if (!$role) {
            $this->error("Role '{$roleIdentifier}' not found.");
            return 1;
        }

        // Validate permissions exist
        $validPermissions = Permission::whereIn('name', $permissionNames)->pluck('name')->toArray();
        $invalidPermissions = array_diff($permissionNames, $validPermissions);

        if (!empty($invalidPermissions)) {
            $this->error('Invalid permissions: ' . implode(', ', $invalidPermissions));
            return 1;
        }

        // Assign permissions
        if ($sync) {
            $role->syncPermissions($validPermissions);
            $this->info("Synced permissions for role '{$role->name}': " . implode(', ', $validPermissions));
        } else {
            $role->givePermissionTo($validPermissions);
            $this->info("Added permissions to role '{$role->name}': " . implode(', ', $validPermissions));
        }

        return 0;
    }
}
