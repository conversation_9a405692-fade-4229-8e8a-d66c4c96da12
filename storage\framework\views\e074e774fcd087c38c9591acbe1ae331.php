<?php $__env->startSection('title', 'Add New Diagnosis'); ?>

<?php $__env->startSection('content'); ?>
<main class="app-main">
    <div class="app-content-header">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-6">
                    <h3 class="mb-0">Add New Diagnosis</h3>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?php echo e(route('diagnoses-management.index')); ?>">Diagnoses Management</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Add New Diagnosis</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="app-content">
        <div class="container-fluid">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="bi bi-plus-circle me-2"></i>
                                Create New Diagnosis
                            </h3>
                        </div>
                        <form action="<?php echo e(route('diagnoses-management.store')); ?>" method="POST">
                            <?php echo csrf_field(); ?>
                            <div class="card-body">
                                <!-- Basic Information -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="code" class="form-label">
                                                Diagnosis Code <span class="text-danger">*</span>
                                            </label>
                                            <input type="text"
                                                   class="form-control <?php $__errorArgs = ['code'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                   id="code"
                                                   name="code"
                                                   value="<?php echo e(old('code')); ?>"
                                                   placeholder="e.g., F32.9"
                                                   required>
                                            <?php $__errorArgs = ['code'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                            <div class="form-text">
                                                Enter the standard diagnostic code (ICD-10, DSM-5, etc.)
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="category_id" class="form-label">
                                                Category <span class="text-danger">*</span>
                                            </label>
                                            <select class="form-select <?php $__errorArgs = ['category_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                    id="category_id"
                                                    name="category_id"
                                                    required>
                                                <option value="">Select a category</option>
                                                <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($category['id']); ?>"
                                                            <?php echo e(old('category_id') == $category['id'] ? 'selected' : ''); ?>>
                                                        <?php echo e($category['name']); ?>

                                                    </option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                            <?php $__errorArgs = ['category_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="name" class="form-label">
                                        Diagnosis Name <span class="text-danger">*</span>
                                    </label>
                                    <input type="text"
                                           class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                           id="name"
                                           name="name"
                                           value="<?php echo e(old('name')); ?>"
                                           placeholder="Enter the full diagnosis name"
                                           required>
                                    <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div class="mb-3">
                                    <label for="description" class="form-label">Description</label>
                                    <textarea class="form-control <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                              id="description"
                                              name="description"
                                              rows="4"
                                              maxlength="1000"
                                              placeholder="Enter a detailed description of the diagnosis"><?php echo e(old('description')); ?></textarea>
                                    <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    <div class="form-text">
                                        Provide additional details about the diagnosis, symptoms, or criteria (max 1000 characters)
                                        <span class="float-end">
                                            <span id="descriptionCount">0</span>/1000
                                        </span>
                                    </div>
                                </div>

                                <!-- Severity and Status -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="severity_level" class="form-label">
                                                Severity Level <span class="text-danger">*</span>
                                            </label>
                                            <select class="form-select <?php $__errorArgs = ['severity_level'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                    id="severity_level"
                                                    name="severity_level"
                                                    required>
                                                <option value="">Select severity level</option>
                                                <option value="mild" <?php echo e(old('severity_level') == 'mild' ? 'selected' : ''); ?>>
                                                    Mild
                                                </option>
                                                <option value="moderate" <?php echo e(old('severity_level') == 'moderate' ? 'selected' : ''); ?>>
                                                    Moderate
                                                </option>
                                                <option value="severe" <?php echo e(old('severity_level') == 'severe' ? 'selected' : ''); ?>>
                                                    Severe
                                                </option>
                                                <option value="critical" <?php echo e(old('severity_level') == 'critical' ? 'selected' : ''); ?>>
                                                    Critical
                                                </option>
                                            </select>
                                            <?php $__errorArgs = ['severity_level'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="is_active" class="form-label">Status</label>
                                            <div class="form-check form-switch">
                                                <input class="form-check-input"
                                                       type="checkbox"
                                                       id="is_active"
                                                       name="is_active"
                                                       value="1"
                                                       <?php echo e(old('is_active', true) ? 'checked' : ''); ?>>
                                                <label class="form-check-label" for="is_active">
                                                    Active (Available for use)
                                                </label>
                                            </div>
                                            <div class="form-text">
                                                Inactive diagnoses will not be available for selection
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Additional Information -->
                                <div class="alert alert-info">
                                    <h6 class="alert-heading">
                                        <i class="bi bi-info-circle me-1"></i>
                                        Important Notes
                                    </h6>
                                    <ul class="mb-0">
                                        <li>Ensure the diagnosis code follows standard medical coding practices</li>
                                        <li>The diagnosis name should be clear and unambiguous</li>
                                        <li>Select the appropriate category and severity level</li>
                                        <li>Inactive diagnoses can be reactivated later if needed</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="card-footer">
                                <div class="d-flex justify-content-between">
                                    <a href="<?php echo e(route('diagnoses-management.index')); ?>" class="btn btn-secondary">
                                        <i class="bi bi-arrow-left me-1"></i>
                                        Back to List
                                    </a>
                                    <div>
                                        <button type="reset" class="btn btn-outline-secondary me-2">
                                            <i class="bi bi-arrow-clockwise me-1"></i>
                                            Reset
                                        </button>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="bi bi-check-lg me-1"></i>
                                            Create Diagnosis
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
// Form validation and enhancement
document.addEventListener('DOMContentLoaded', function() {
    // Auto-format diagnosis code
    const codeInput = document.getElementById('code');
    codeInput.addEventListener('input', function() {
        this.value = this.value.toUpperCase();
    });

    // Character counting for description
    const descriptionTextarea = document.getElementById('description');
    const descriptionCount = document.getElementById('descriptionCount');

    function updateDescriptionCount() {
        const count = descriptionTextarea.value.length;
        descriptionCount.textContent = count;

        if (count > 900) {
            descriptionCount.parentElement.classList.add('text-warning');
        } else {
            descriptionCount.parentElement.classList.remove('text-warning');
        }

        if (count >= 1000) {
            descriptionCount.parentElement.classList.add('text-danger');
            descriptionCount.parentElement.classList.remove('text-warning');
        } else {
            descriptionCount.parentElement.classList.remove('text-danger');
        }
    }

    descriptionTextarea.addEventListener('input', updateDescriptionCount);
    updateDescriptionCount(); // Initial count

    // Dynamic severity level styling
    const severitySelect = document.getElementById('severity_level');
    severitySelect.addEventListener('change', function() {
        const value = this.value;
        this.className = 'form-select';

        if (value === 'mild') {
            this.classList.add('border-success');
        } else if (value === 'moderate') {
            this.classList.add('border-warning');
        } else if (value === 'severe' || value === 'critical') {
            this.classList.add('border-danger');
        }
    });

    // Form submission confirmation and validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        const code = document.getElementById('code').value;
        const name = document.getElementById('name').value;
        const categoryId = document.getElementById('category_id').value;
        const severityLevel = document.getElementById('severity_level').value;

        // Client-side validation
        if (!code.trim()) {
            e.preventDefault();
            toastr.error('Diagnosis code is required.');
            document.getElementById('code').focus();
            return;
        }

        if (!name.trim()) {
            e.preventDefault();
            toastr.error('Diagnosis name is required.');
            document.getElementById('name').focus();
            return;
        }

        if (!categoryId) {
            e.preventDefault();
            toastr.error('Please select a category.');
            document.getElementById('category_id').focus();
            return;
        }

        if (!severityLevel) {
            e.preventDefault();
            toastr.error('Please select a severity level.');
            document.getElementById('severity_level').focus();
            return;
        }

        // Show confirmation with toastr-style confirmation
        if (!confirm(`Are you sure you want to create diagnosis "${code} - ${name}"?`)) {
            e.preventDefault();
            return;
        }

        // Show loading state
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Creating...';
        submitBtn.disabled = true;

        // Show info message
        toastr.info('Creating diagnosis... Please wait.');
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\Marbar-Africa\Marbar-AFRICA\resources\views/admin/management/diagnoses/create.blade.php ENDPATH**/ ?>